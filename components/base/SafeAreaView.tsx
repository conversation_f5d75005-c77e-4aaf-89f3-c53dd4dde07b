import React from 'react';

import { SafeAreaView as NativeSafeAreaView, SafeAreaViewProps } from 'react-native-safe-area-context';

import { useThemeColorLegacy } from '@/hooks/useThemeColor';

type Props = SafeAreaViewProps;

export function SafeAreaView({ children, style, ...props }: Props) {
  const theme = useThemeColorLegacy();

  return (
    <NativeSafeAreaView
      {...props}
      style={[
        {
          backgroundColor: theme.backgroundPrimary,
          flex: 1,
        },
        style,
      ]}
    >
      {children}
    </NativeSafeAreaView>
  );
}
