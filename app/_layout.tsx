import React from 'react';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useFonts } from 'expo-font';
import { Slot, SplashScreen } from 'expo-router';
import 'react-native-gesture-handler';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { SnackbarProvider } from '@/components/global/Snackbar';
import { ThemedView } from '@/components/global/ThemedView';
import { ThemeProviderContext } from '@/context/ThemeContext';
import '@/i18n';

import '../global.css';

export const queryClient = new QueryClient();

export default function Layout() {
  const [loaded] = useFonts({
    OutfitRegular: require('../assets/fonts/Outfit-Regular.ttf'),
    OutfitSemiBold: require('../assets/fonts/Outfit-SemiBold.ttf'),
    OutfitBold: require('../assets/fonts/Outfit-Bold.ttf'),
    TintRegular: require('../assets/fonts/Inter_24pt-Regular.ttf'),
    TintSemiBold: require('../assets/fonts/Inter_24pt-SemiBold.ttf'),
    TintBold: require('../assets/fonts/Inter_24pt-Bold.ttf'),
  });
  React.useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <SafeAreaProvider>
        <GestureHandlerRootView>
          <ThemeProviderContext>
            <SnackbarProvider>
              <ThemedView style={{ flex: 1 }} useGradient={true}>
                <Slot />
              </ThemedView>
            </SnackbarProvider>
          </ThemeProviderContext>
        </GestureHandlerRootView>
      </SafeAreaProvider>
    </QueryClientProvider>
  );
}
